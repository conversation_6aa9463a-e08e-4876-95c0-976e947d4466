using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using FairyGUI;
using UnityEngine;
using UnityEngine.SceneManagement;

public class BattleHandler : HandlerBase
{
    private static string BattleSceneUrl = "Scenes/Battle";
    private BattlePanel battlePanel;

    public BattlePanel BattlePanel => battlePanel;

    private string curBattleSceneName;
    /// <summary>
    /// 游戏剩余时间
    /// </summary>
    protected int gameLeftTime;
    /// <summary>
    /// 是否游戏结束
    /// </summary>
    protected bool isGameOver;
    private bool isGameWin;
    private bool isPause;
    private bool isFirstClick;  // 添加标志位

    private InfoGate infoGate;

    private LifeSystem lifeSystem;
    private CountdownSystem countdownSystem;
    private DailyChallengeSystem dailyChallengeSystem;

    private GameProgressSystem gameProgressSystem;
    private bool isRefreshShowing;
    public override void OnEnter()
    {
        SoundManager.PlayBg("bgm");
        // LoadingPanel.Show();

        var level = GameGlobal.EnterLevel;
        Report.Instance.ReportChapterPlay(level, Session.IsRestart);
        Session.IsRestart = false;

        infoGate = ConfigGate.GetData(level, GameGlobal.BattleType);
        curBattleSceneName = BattleSceneUrl;
        // if (infoGate.isBoss)
        // {
        //     curBattleSceneName += "Boss";
        // }

        LoadingPanel.SetProgress(0.9f);
        AssetBundleManager.LoadScene(curBattleSceneName, LoadSceneMode.Additive, OnScenePrefabLoaded);

        gameProgressSystem = SystemManager.Inst.GetGlobalSystem<GameProgressSystem>();
    }

    private void OnScenePrefabLoaded()
    {
        LoadingPanel.SetProgress(0.91f);
        Panel.Create((BattlePanel panel) =>
        {
            battlePanel = panel;
            InitializeGame();
            LoadingPanel.SetProgress(0.92f);
            Timers.inst.CallLater(PrepareResources);//ui自适应

            Platform.Instance.OnApplicationFocus += OnApplicationFocus;
        });

        if (GameGlobal.EnterLevel >= 2)//预加载结算收集界面
        {
            FUILoader.LoadPackage("ChangeTheme");
        }
    }

    private void InitializeGame()
    {
        // currentLevel = 1;
        // curCompletedIdioms = 0;
        // allQuestions = new List<IdiomQuestion>();
        // playerAreaCardList = new List<char>();
        // tempStorage = new List<char>();
    }

    async public void PrepareResources(object obj)
    {
        await BattleResources.Inst.PreLoad((radio) =>
        {
            LoadingPanel.SetProgress(0.93f + 0.07f * radio);
        });

        StartGame();
    }

    public void StartGame()
    {
        GameGlobal.WinGold = infoGate.gold;
        GameGlobal.GameDurationTime = infoGate.gameTime;

        BattleScene.Inst.battleHandler = this;
        BattleScene.Inst.OnTileInit += OnSceneTileInit;
        BattleScene.Inst.OnTileSelected += OnSceneTileSelected;
        BattleScene.Inst.OnTileMatched += OnSceneTileMatched;
        BattleScene.Inst.OnGameDone += OnGameDone;
        BattleScene.Inst.OnNoSolution += OnNoSolution;
        BattleScene.Inst.OnCombo += OnCombo;
        BattleScene.Inst.OnUseHammer += OnUseHammer;

        isRefreshShowing = false;
        // infoGate.gravityDirection = GravityDirection.UD2Center;//test

        // 尝试恢复游戏进度
        var progressData = gameProgressSystem.GetProgressData();
        var liveCount = 0;
        int[] boardData = null;
        var cacheTotalTileCount = 0;
        if (progressData != null)
        {
            gameLeftTime = (int)progressData.remainingTime;
            liveCount = progressData.currentLife;
            boardData = progressData.boardData.gridData;
            cacheTotalTileCount = progressData.totalTileCount;
            Report.Instance.RestoreReportProgress(progressData.reportProgress);
            gameProgressSystem.ClearProgress();

        }
        else
        {
            gameLeftTime = infoGate.gameTime;
            Report.Instance.RestoreReportProgress(string.Empty);
        }

        if (GameGlobal.BattleType == BattleType.DailyChallenge)
        {
            dailyChallengeSystem = SystemManager.Inst.GetGlobalSystem<DailyChallengeSystem>();
        }

        if (infoGate.IsGuide())
        {
            BattleScene.Inst.maxAutoPromptPairs = 0;
            var types = infoGate.boardInfo;
            BattleScene.Inst.InitGrid(types, infoGate, 2, 3, cacheTotalTileCount, OnGridCreated: () =>
            {
                LoadingPanel.HideWhenFullProgress();
            });
        }
        else
        {
            BattleScene.Inst.InitGrid(boardData, infoGate, 2, 5, cacheTotalTileCount, OnGridCreated: () =>
            {
                LoadingPanel.HideWhenFullProgress();
            });
        }

        if (infoGate.limitLife > 0)
        {
            BattleScene.Inst.isLimitLife = true;
            lifeSystem = SystemManager.Inst.GetGameplaySystem<LifeSystem>();
            lifeSystem.OnLifeChanged += OnLifeChanged;
            lifeSystem.OnLifeEmpty += OnLifeEmpty;
        }

        if (infoGate.gameTime > 0)
        {
            countdownSystem = SystemManager.Inst.GetGameplaySystem<CountdownSystem>();
            countdownSystem.OnTimeChanged += OnTimeChanged;
            countdownSystem.OnTimeUp += OnTimeUp;
        }

        battlePanel.StartGame(this);

        if (countdownSystem != null)
        {
            countdownSystem?.StartCountdown(gameLeftTime);
            countdownSystem?.Pause();
            battlePanel.ShowLeftTime(true);
        }
        else
        {
            battlePanel.ShowLeftTime(false);
        }

        if (lifeSystem != null)
        {
            if (liveCount > 0)
            {
                lifeSystem.SetLives(liveCount);
            }
            battlePanel.UpdateLife(lifeSystem.GetLives());
        }
        else
        {
            battlePanel.UpdateLife(0);
        }


        // NotifyMgr.On(NotifyNames.Revival, this, OnRevival);
        // NotifyMgr.On(NotifyNames.AddGameTime, this, OnAddGameTime);
        NotifyMgr.On(NotifyNames.ContinueGame, this, OnContinueGame);
    }

    private void Update()
    {
        if (isPause)
            return;

        Report.Instance.UpdateGamePlayedTime(Time.deltaTime);
    }

    private void OnSceneTileInit(BoardCell[,] tiles)
    {
        isGameWin = false;
        isGameOver = false;
        isFirstClick = false;  // 重置标志位

        if (tiles != null)
        {
            if (infoGate.IsGuide())
            {
                GuideManager.Inst.EnqueueStep(GuideSetting.GetNewPlayerGuide(tiles), (step) =>
                {
                    Report.Instance.ReportGuideComplete(step);
                });
            }

            if (infoGate.gateEntry != 0)
            {
                Panel.Create((GateEntryPanel panel) =>
                {
                    var gateEntry = infoGate.gateEntry;
                    var startFlyPos = panel.GetStartFlyPos();

                    panel.OnClosed = (type) =>
                    {
                        battlePanel.FlyGravityArrow(gateEntry, startFlyPos);
                    };
                    panel.SetData(gateEntry);
                });
            }
        }
        battlePanel?.OnSceneTileInit();
    }

    private void OnSceneTileSelected(BoardCell tile)
    {
        if (!isFirstClick)
        {
            isFirstClick = true;
            countdownSystem?.Resume();  // 开始倒计时
        }

        Report.Instance.IncrementOperateStep();
    }

    private void OnSceneTileMatched(bool isMatchSuccessful)
    {
        if (!isMatchSuccessful)
        {
            lifeSystem?.ConsumeLife();
        }
        battlePanel?.OnTileMatched(isMatchSuccessful);
    }

    private void OnLifeChanged(int lifeCount)
    {
        battlePanel.UpdateLife(lifeCount);
    }

    private void OnLifeEmpty()
    {
        battlePanel?.ShowResult(FailReason.LifeEmpty);
    }

    private void OnTimeChanged(float remainingTime)
    {
        battlePanel?.UpdateLeftTime(remainingTime);
    }

    private void OnTimeUp()
    {
        battlePanel?.ShowResult(FailReason.TimeOut);
    }

    public void SaveGameProgress()
    {
        if (infoGate == null || infoGate.IsGuide())//引导关卡不处理
            return;
        if (!isGameOver && !isGameWin)
        {
            gameProgressSystem?.SaveProgress();
        }
    }

    private void OnContinueGame(object failReason)
    {
        var reason = (FailReason)failReason;
        if (reason == FailReason.TimeOut)
        {
            countdownSystem?.AddTime(ConfigSetting.addTimeSecond);
        }
        else if (reason == FailReason.LifeEmpty)
        {
            lifeSystem?.Reset();
        }
    }

    // private void OnAddGameTime(object data)
    // {
    //     int addTime = (int)data;
    //     countdownSystem.AddTime(addTime);
    // }

    // private void OnRevival()
    // {
    //     isGameOver = false;
    //     battlePanel.Revival();
    // }

    public void Pause()
    {
        isPause = true;
        countdownSystem?.Pause();
    }

    public void Resume()
    {
        if (!isFirstClick)
            return;

        isPause = false;
        countdownSystem?.Resume();
    }

    private void OnUseHammer(Vector3 scenePos)
    {
        battlePanel?.PlayHammers(scenePos);
    }
    private void OnCombo(Vector3 scenePos, int comboCount)
    {
        battlePanel?.PlayComboEffect(scenePos, comboCount);
    }

    async private void OnNoSolution(int curCount, int totalCount)
    {
        TipMgr.ShowTip(LangUtil.GetText("txtNoSolution"));//已经没有可消除的砖块
        await UniTask.Delay(1500);

        if (this == null || isRefreshShowing)
            return;

        isRefreshShowing = true;
        Panel.Create((ResultFailRefresh panel) =>
        {
            panel.OnClosed = (type) =>
            {
                isRefreshShowing = false;
                if (type == 0)
                {
                    battlePanel.OnHasSolution(false);
                }
            };
            panel.SetData(curCount, totalCount);
        });
    }

    public void OnGameDone(bool isWin)
    {
        if (isGameOver)
            return;
        isGameOver = true;

        if (isWin)
        {
            battlePanel?.GameWin();
            if (GameGlobal.BattleType == BattleType.Normal)
            {
                GameGlobal.IncrementPassLevel();
                GameGlobal.IncrementLevel();
            }
            else
            {
                dailyChallengeSystem.CompleteDailyChallenge();
            }

            // 上报关卡结束
            Report.Instance.ReportChapterEnd(true, "通关");


            if (infoGate.IsGuide())
            {
                //引导关直接进入第二关
                new CmdEnterBattle().Execute(2, false);
                Platform.Instance.RequestStrengthFeedSubscribe();
            }
            else
            {
                new CmdShowBattleResult().Execute();
            }
        }
    }

    public void CheckWin(bool isWin = false)
    {
        // if (isWin)
        // {
        //     OnGameDone(true); // 游戏胜利
        // }
        // else if (tempStorage.Count >= openSlotCount)
        // {
        //     OnGameDone(false); // 游戏失败
        // }
    }

    public override void OnExit()
    {
        // 先清理事件监听
        if (BattleScene.Inst != null)
        {
            BattleScene.Inst.OnTileInit -= OnSceneTileInit;
            BattleScene.Inst.OnTileSelected -= OnSceneTileSelected;
            BattleScene.Inst.OnTileMatched -= OnSceneTileMatched;
            BattleScene.Inst.OnGameDone -= OnGameDone;
            BattleScene.Inst.OnNoSolution -= OnNoSolution;
            BattleScene.Inst.OnCombo -= OnCombo;
            BattleScene.Inst.OnUseHammer -= OnUseHammer;
        }

        // 清理资源
        BattleResources.Inst.Dispose();
        GuideManager.Inst.Clear();
        SystemFacade.ClearGameplaySystemCache();

        // 清理其他事件监听
        Platform.Instance.OnApplicationFocus -= OnApplicationFocus;
        NotifyMgr.OffAllCaller(this);

        battlePanel?.Hide();

        // 强制GC回收
        Resources.UnloadUnusedAssets();

        // 卸载场景
        AssetBundleManager.UnloadScene(curBattleSceneName);
    }

    internal bool UseItem(int itemId)
    {
        return BattleScene.Inst.UseItem(itemId);
    }

    private void OnApplicationFocus(bool focusStatus)
    {
        Log.Info("OnApplicationFocus:" + focusStatus);
        if (!focusStatus)
        {
            SaveGameProgress();
        }
    }
}