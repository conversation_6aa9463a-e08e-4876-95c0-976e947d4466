﻿using FairyGUI;
using UnityEngine;

public class LoadingPanel
{
    private LoadingPanel() { }

    public static float speed = 100;
    public static float fullSpeed = 300;
    private static float currentValue;
    private static float targetValue;
    private static float maxValue;
    private static GLoader logo;
    private static GImage loadingIcon;
    private static float barWidth;
    private static float barX;

    public static bool isShow;
    public static void Show()
    {
        if (isShow)
            return;

        isShow = true;
        _hideWhenFullProgress = false;
        Instance._Show();
        currentValue = 0;
        targetValue = 0;
        maxValue = 100;
        loadingIcon.x = barX;
        SetProgress(1);
        if (progress != null)
        {
            progress.value = 0;
        }
        Timers.inst.AddUpdate(OnUpdate);
    }

    private static void OnUpdate(object param)
    {
        if (progress != null)
        {
            var currentSpeed = _hideWhenFullProgress ? fullSpeed : speed;
            currentValue = Mathf.Min(currentValue + currentSpeed * Time.unscaledDeltaTime, targetValue);

            progress.value = currentValue;
            loadingIcon.x = barX + barWidth * currentValue / maxValue;
            if (progress.value >= maxValue && _hideWhenFullProgress)
            {
                Hide();
            }
        }
    }

    public static void Hide()
    {
        isShow = false;
        Timers.inst.Remove(OnUpdate);
        Instance._Hide();
    }

    public static bool IsShow() { return isShow; }

    private static bool _hideWhenFullProgress;
    public static void HideWhenFullProgress()
    {
        _hideWhenFullProgress = true;
    }

    public static void SetProgress(float value)
    {
        targetValue = value * maxValue;
        Log.Info($"currentvalue  target==>{targetValue}");
    }


    private GComponent contentPane;
    private void _Show()
    {
        GRoot.inst.AddChild(contentPane);
    }
    private void _Hide()
    {
        contentPane.RemoveFromParent();
    }

    private static LoadingPanel _instance;
    private static GProgressBar progress;
    private static LoadingPanel Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new LoadingPanel();
                UIPackage.AddPackage("FGUI/Loading/Loading");
                _instance.contentPane = UIPackage.CreateObject("Loading", "LoadingPanel").asCom;
                _instance.contentPane.sortingOrder = 99999;
                var imgLogo = _instance.contentPane.GetChild("imgLogo").asLoader;
#if WXGAME
                imgLogo.url = "ui://Loading/logo";
#else
                imgLogo.url = "ui://Loading/logo2";
#endif
                progress = _instance.contentPane.GetChild("progressBar").asProgress;
                var bar = progress.GetChild("bar");
                barX = bar.x;
                barWidth = bar.initWidth;
                loadingIcon = progress.GetChild("icon").asImage;

            }
            _instance.contentPane.MakeFullScreen();
            return _instance;
        }
    }

}
